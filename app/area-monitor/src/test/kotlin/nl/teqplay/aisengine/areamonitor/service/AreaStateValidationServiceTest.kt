package nl.teqplay.aisengine.areamonitor.service

import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.areamonitor.config
import nl.teqplay.aisengine.areamonitor.diff
import nl.teqplay.aisengine.areamonitor.model.State
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.areamonitor.service.config.AreaConfigService
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import java.time.Instant

class AreaStateValidationServiceTest {

    private val stateService = mockk<StateService>()
    private val areaSearchService = mockk<AreaSearchService>()
    private val configService = mockk<AreaConfigService>()
    private val eventCreationService = mockk<EventCreationService>()
    private val consumerPublishService = mockk<ConsumerPublishService>()

    private lateinit var validationService: AreaStateValidationService

    private val testMmsi = 123456789
    private val testConfigId = "test-config-id"
    private val testStartEventId = "test-start-event-id"
    private val testLocation = Location(52.0, 4.0)
    private val testConfig = config()
    private val testDiff = diff(mmsi = testMmsi)

    @BeforeEach
    fun setUp() {
        validationService = AreaStateValidationService(
            stateService = stateService,
            areaSearchService = areaSearchService,
            configService = configService,
            eventCreationService = eventCreationService,
            consumerPublishService = consumerPublishService
        )
    }

    @Test
    fun `validateAreaState should handle empty active start events`() {
        // Given
        every { stateService.getAllActiveStartEvents() } returns emptyList()

        // When & Then
        assertDoesNotThrow {
            validationService.validateAreaState()
        }

        verify { stateService.getAllActiveStartEvents() }
    }

    @Test
    fun `validateAreaState should skip ships with no latest position`() {
        // Given
        val startEvent = State.StartEvent(
            diff = testDiff,
            startEventId = testStartEventId,
            lastStateEventSentTime = null
        )
        val startEventData = StateService.StartEventData(testConfigId, testMmsi, startEvent)

        every { stateService.getAllActiveStartEvents() } returns listOf(startEventData)
        every { configService.getConfig(testConfigId) } returns testConfig
        every { stateService.getLatestShipPosition(testMmsi) } returns null

        // When
        validationService.validateAreaState()

        // Then
        verify { stateService.getAllActiveStartEvents() }
        verify { configService.getConfig(testConfigId) }
        verify { stateService.getLatestShipPosition(testMmsi) }
        verify(exactly = 0) { areaSearchService.isInsideArea(any(), any(), any()) }
    }

    @Test
    fun `validateAreaState should skip ships still inside area`() {
        // Given
        val startEvent = State.StartEvent(
            diff = testDiff,
            startEventId = testStartEventId,
            lastStateEventSentTime = null
        )
        val startEventData = StateService.StartEventData(testConfigId, testMmsi, startEvent)
        val shipPosition = StateService.ShipPosition(testLocation, Instant.now())

        every { stateService.getAllActiveStartEvents() } returns listOf(startEventData)
        every { configService.getConfig(testConfigId) } returns testConfig
        every { stateService.getLatestShipPosition(testMmsi) } returns shipPosition
        every { areaSearchService.isInsideArea(testLocation, testConfig, false) } returns true

        // When
        validationService.validateAreaState()

        // Then
        verify { stateService.getAllActiveStartEvents() }
        verify { configService.getConfig(testConfigId) }
        verify { stateService.getLatestShipPosition(testMmsi) }
        verify { areaSearchService.isInsideArea(testLocation, testConfig, false) }
        verify(exactly = 0) { eventCreationService.createEndEvent(any(), any(), any()) }
    }

    @Test
    fun `validateAreaState should send missed end event for ship outside area`() {
        // Given
        val startEvent = State.StartEvent(
            diff = testDiff,
            startEventId = testStartEventId,
            lastStateEventSentTime = null
        )
        val startEventData = StateService.StartEventData(testConfigId, testMmsi, startEvent)
        val shipPosition = StateService.ShipPosition(testLocation, Instant.now())
        val endEvent = mockk<AreaEndEvent>()

        every { stateService.getAllActiveStartEvents() } returns listOf(startEventData)
        every { configService.getConfig(testConfigId) } returns testConfig
        every { stateService.getLatestShipPosition(testMmsi) } returns shipPosition
        every { areaSearchService.isInsideArea(testLocation, testConfig, false) } returns false
        every { eventCreationService.createEndEvent(testConfig, any(), testStartEventId) } returns endEvent
        every { consumerPublishService.save(any(), testConfig, endEvent) } returns Unit
        every { consumerPublishService.publish(any(), any()) } returns Unit

        // When
        validationService.validateAreaState()

        // Then
        verify { stateService.getAllActiveStartEvents() }
        verify { configService.getConfig(testConfigId) }
        verify { stateService.getLatestShipPosition(testMmsi) }
        verify { areaSearchService.isInsideArea(testLocation, testConfig, false) }
        verify { eventCreationService.createEndEvent(testConfig, any(), testStartEventId) }
        verify { consumerPublishService.save(any(), testConfig, endEvent) }
        verify { consumerPublishService.publish(any(), any()) }
    }

    @Test
    fun `validateAreaState should handle missing config gracefully`() {
        // Given
        val startEvent = State.StartEvent(
            diff = testDiff,
            startEventId = testStartEventId,
            lastStateEventSentTime = null
        )
        val startEventData = StateService.StartEventData(testConfigId, testMmsi, startEvent)

        every { stateService.getAllActiveStartEvents() } returns listOf(startEventData)
        every { configService.getConfig(testConfigId) } returns null

        // When
        validationService.validateAreaState()

        // Then
        verify { stateService.getAllActiveStartEvents() }
        verify { configService.getConfig(testConfigId) }
        verify(exactly = 0) { stateService.getLatestShipPosition(any()) }
        verify(exactly = 0) { areaSearchService.isInsideArea(any(), any(), any()) }
    }

    @Test
    fun `validateAreaState should create synthetic diff message with correct data`() {
        // Given
        val startEvent = State.StartEvent(
            diff = testDiff,
            startEventId = testStartEventId,
            lastStateEventSentTime = null
        )
        val startEventData = StateService.StartEventData(testConfigId, testMmsi, startEvent)
        val shipPosition = StateService.ShipPosition(testLocation, Instant.now())
        val endEvent = mockk<AreaEndEvent>()
        val capturedDiff = slot<AisDiffMessage>()

        every { stateService.getAllActiveStartEvents() } returns listOf(startEventData)
        every { configService.getConfig(testConfigId) } returns testConfig
        every { stateService.getLatestShipPosition(testMmsi) } returns shipPosition
        every { areaSearchService.isInsideArea(testLocation, testConfig, false) } returns false
        every { eventCreationService.createEndEvent(testConfig, capture(capturedDiff), testStartEventId) } returns endEvent
        every { consumerPublishService.save(any(), testConfig, endEvent) } returns Unit
        every { consumerPublishService.publish(any(), any()) } returns Unit

        // When
        validationService.validateAreaState()

        // Then
        val syntheticDiff = capturedDiff.captured
        assert(syntheticDiff.mmsi == testMmsi)
        assert(syntheticDiff.location.latest() == testLocation)
        assert(syntheticDiff.sources.contains("area-state-validation"))
        assert(syntheticDiff.oldMessageTime == shipPosition.messageTime)
    }

    @Test
    fun `validateAreaState should handle multiple ships with mixed scenarios`() {
        // Given
        val startEvent1 = State.StartEvent(diff = testDiff, startEventId = "event1", lastStateEventSentTime = null)
        val startEvent2 = State.StartEvent(diff = testDiff.copy(mmsi = 987654321), startEventId = "event2", lastStateEventSentTime = null)
        
        val startEventData1 = StateService.StartEventData("config1", testMmsi, startEvent1)
        val startEventData2 = StateService.StartEventData("config2", 987654321, startEvent2)
        
        val shipPosition1 = StateService.ShipPosition(testLocation, Instant.now())
        val shipPosition2 = StateService.ShipPosition(Location(53.0, 5.0), Instant.now())
        val endEvent = mockk<AreaEndEvent>()

        every { stateService.getAllActiveStartEvents() } returns listOf(startEventData1, startEventData2)
        every { configService.getConfig("config1") } returns testConfig
        every { configService.getConfig("config2") } returns testConfig.copy(_id = "config2")
        every { stateService.getLatestShipPosition(testMmsi) } returns shipPosition1
        every { stateService.getLatestShipPosition(987654321) } returns shipPosition2
        every { areaSearchService.isInsideArea(testLocation, any(), false) } returns true // Ship 1 still inside
        every { areaSearchService.isInsideArea(Location(53.0, 5.0), any(), false) } returns false // Ship 2 outside
        every { eventCreationService.createEndEvent(any(), any(), "event2") } returns endEvent
        every { consumerPublishService.save(any(), any(), endEvent) } returns Unit
        every { consumerPublishService.publish(any(), any()) } returns Unit

        // When
        validationService.validateAreaState()

        // Then
        verify { eventCreationService.createEndEvent(any(), any(), "event2") }
        verify(exactly = 1) { consumerPublishService.save(any(), any(), endEvent) }
        verify(exactly = 1) { consumerPublishService.publish(any(), any()) }
    }
}
