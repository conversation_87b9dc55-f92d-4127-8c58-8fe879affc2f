package nl.teqplay.aisengine.areamonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.areamonitor.service.config.AreaConfigService
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.system.measureTimeMillis

/**
 * Service that periodically validates area state and handles missed end events.
 * 
 * Every 6 hours, this service:
 * 1. Goes through all ships with active start events in areas
 * 2. Checks if each ship is still in its respective area using latest position
 * 3. For ships that have left areas, sends missed end events
 * 4. Cleans up invalid state entries
 */
@Component
class AreaStateValidationService(
    private val stateService: StateService,
    private val areaSearchService: AreaSearchService,
    private val configService: AreaConfigService,
    private val eventCreationService: EventCreationService,
    private val consumerPublishService: ConsumerPublishService
) {
    
    private val log = KotlinLogging.logger { }
    
    /**
     * Scheduled validation task that runs every 6 hours.
     * Initial delay of 30 minutes to allow system to fully initialize.
     */
    @Scheduled(fixedRate = 6 * 60 * 60 * 1000L, initialDelay = 30 * 60 * 1000L) // 6 hours in milliseconds
    fun validateAreaState() {
        log.info { "Starting area state validation..." }
        
        val timeTaken = measureTimeMillis {
            try {
                val validationResults = performValidation()
                log.info { 
                    "Area state validation completed. " +
                    "Checked ${validationResults.totalChecked} ships, " +
                    "found ${validationResults.missedEndEvents} ships outside areas, " +
                    "sent ${validationResults.eventsPublished} missed end events"
                }
            } catch (e: Exception) {
                log.error(e) { "Error during area state validation" }
            }
        }
        
        log.info { "Area state validation completed in ${timeTaken}ms" }
    }
    
    /**
     * Performs the actual validation logic
     */
    private fun performValidation(): ValidationResults {
        var totalChecked = 0
        var missedEndEvents = 0
        var eventsPublished = 0
        
        // Get all active start events from state service
        val activeStartEvents = stateService.getAllActiveStartEvents()
        
        for ((configId, mmsi, startEvent) in activeStartEvents) {
            totalChecked++
            
            // Get the area configuration
            val config = configService.getConfig(configId)
            if (config == null) {
                log.warn { "Config not found for configId: $configId, cleaning up state for MMSI: $mmsi" }
                cleanupInvalidState(configId, mmsi)
                continue
            }
            
            // Get the latest position for this ship
            val latestPosition = stateService.getLatestShipPosition(mmsi)
            if (latestPosition == null) {
                log.debug { "No latest position found for MMSI: $mmsi in area: ${config.area.name}" }
                continue
            }
            
            // Check if the ship is still inside the area
            val isStillInside = areaSearchService.isInsideArea(
                location = latestPosition.location,
                config = config,
                includeOuter = false
            )
            
            if (!isStillInside) {
                log.info { 
                    "Ship MMSI: $mmsi has left area: ${config.area.name} (${config.area.id}), " +
                    "sending missed end event"
                }
                
                // Create and publish the missed end event
                val endEvent = eventCreationService.createEndEvent(
                    config = config,
                    //TODO Is this right?
                    diff = startEvent.diff,
                    startEventId = startEvent.startEventId
                )
                
                // Publish the event and update state
                consumerPublishService.save(startEvent.diff, config, endEvent)
                consumerPublishService.publish(startEvent.diff, listOf(config to endEvent))
                
                missedEndEvents++
                eventsPublished++
            }
        }
        
        return ValidationResults(totalChecked, missedEndEvents, eventsPublished)
    }
    
    /**
     * Creates a synthetic AisDiffMessage for generating end events when ships have left areas
     */
    private fun createSyntheticDiffMessage(
        startEvent: StateService.StartEventData,
        latestPosition: StateService.ShipPosition,
        currentTime: Instant
    ): AisDiffMessage {
        val originalDiff = startEvent.startEvent.diff
        
        return AisDiffMessage(
            mmsi = originalDiff.mmsi,
            messageTime = currentTime,
            oldMessageTime = latestPosition.messageTime,
            sources = setOf("area-state-validation"),
            
            // Use latest position
            location = AisDiffField(latestPosition.location, null),
            
            // Preserve other fields from original diff or set to null if not available
            heading = AisDiffField(originalDiff.heading.latest(), null),
            positionAccuracy = AisDiffField(originalDiff.positionAccuracy.latest(), null),
            speedOverGround = AisDiffField(originalDiff.speedOverGround.latest(), null),
            courseOverGround = AisDiffField(originalDiff.courseOverGround.latest(), null),
            status = AisDiffField(originalDiff.status.latest(), null),
            rateOfTurn = AisDiffField(originalDiff.rateOfTurn.latest(), null),
            specialManeuverStatus = AisDiffField(originalDiff.specialManeuverStatus.latest(), null),
            
            // Static data from original diff
            imo = AisDiffField(originalDiff.imo.latest(), null),
            name = AisDiffField(originalDiff.name.latest(), null),
            callSign = AisDiffField(originalDiff.callSign.latest(), null),
            shipType = AisDiffField(originalDiff.shipType.latest(), null),
            draught = AisDiffField(originalDiff.draught.latest(), null),
            eta = AisDiffField(originalDiff.eta.latest(), null),
            destination = AisDiffField(originalDiff.destination.latest(), null),
            transponderPosition = AisDiffField(originalDiff.transponderPosition.latest(), null),
            positionSensorType = AisDiffField(originalDiff.positionSensorType.latest(), null),
            aisVersion = AisDiffField(originalDiff.aisVersion.latest(), null),
            usingDataTerminal = AisDiffField(originalDiff.usingDataTerminal.latest(), null),
            eni = AisDiffField(originalDiff.eni.latest(), null)
        )
    }
    
    /**
     * Cleans up invalid state for a config/mmsi combination
     */
    private fun cleanupInvalidState(configId: String, mmsi: Int) {
        // This would need to be implemented in StateService
        log.warn { "Cleaning up invalid state for configId: $configId, MMSI: $mmsi" }
        // TODO: Implement cleanup method in StateService if needed
    }
    
    /**
     * Data class to hold validation results
     */
    private data class ValidationResults(
        val totalChecked: Int,
        val missedEndEvents: Int,
        val eventsPublished: Int
    )
}
