package nl.teqplay.aisengine.areamonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.areamonitor.datasource.position.PositionDataSource
import nl.teqplay.aisengine.areamonitor.datasource.state.StateDataSource
import nl.teqplay.aisengine.areamonitor.model.Position
import nl.teqplay.aisengine.areamonitor.model.State
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.areamonitor.service.config.AreaConfigService
import nl.teqplay.skeleton.model.Location
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Instant
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentSkipListSet
import kotlin.system.measureTimeMillis

/**
 * Manages the [State] of all events related to the monitored areas.
 */
@Component
class StateService(
    private val stateDataSource: StateDataSource,
    private val positionDataSource: PositionDataSource,
    private val configService: AreaConfigService
) {

    private val log = KotlinLogging.logger { }

    private val startEventMap = ConcurrentHashMap<Pair<String, Int>, State.StartEvent>()
    private val outsideMap = ConcurrentHashMap<String, MutableSet<Int>>()
    private val mmsiConfigMap = ConcurrentHashMap<Int, MutableSet<String>>()
    private val areasRequiringPersistence = ConcurrentSkipListSet<String>()

    // Lightweight ship position tracking for ALL ships (not just those in areas)
    private val shipPositions = ConcurrentHashMap<Int, ShipPosition>()
    private val positionsRequiringPersistence = ConcurrentSkipListSet<Int>()

    private fun toKey(config: Config, mmsi: Int): Pair<String, Int> = toKey(config._id, mmsi)
    private fun toKey(configId: String, mmsi: Int): Pair<String, Int> = configId to mmsi

    /**
     * Initialize in-memory state from database
     */
    @PostConstruct
    fun initialize() {
        log.info { "Using previous area state..." }
        val timeTaken = measureTimeMillis {
            val configIds = configService.getConfigIds()
            loadLocally(configIds)
        }
        log.info { "Restored area state for ${startEventMap.size} events in ${timeTaken}ms" }

        log.info { "Loading ship positions..." }
        val positionTimeTaken = measureTimeMillis {
            loadShipPositions()
        }
        log.info { "Restored ${shipPositions.size} ship positions in ${positionTimeTaken}ms" }
    }

    @Scheduled(initialDelayString = "\${persist.interval}", fixedDelayString = "\${persist.interval}")
    fun persistOnSchedule() {
        persist()
    }

    fun shutdown() {
        persist()
    }

    /**
     * persists the in-memory state to the database
     */
    fun persist() {
        log.info { "Persisting area state for ${startEventMap.size} start events..." }
        val timeTaken = measureTimeMillis {
            stateDataSource.save(getOngoingAndClearedStateList()).also {
                areasRequiringPersistence.clear()
            }
        }
        log.info { "Persisted area state in ${timeTaken}ms" }

        log.info { "Persisting ${positionsRequiringPersistence.size} ship positions..." }
        val shipPositionsTimeTaken = measureTimeMillis {
            persistShipPositions()
        }
        log.info { "Persisted ship positions in ${shipPositionsTimeTaken}ms" }
    }

    /**
     * Helper method, combining both ongoing and cleared state
     */
    fun getOngoingAndClearedStateList(): List<State> {
        val ongoingState = getStateList()
        val clearedState = getClearedAreaList(ongoingState)
        return ongoingState + clearedState
    }

    /**
     * Get all state, or only those which match areas with specific [ids].
     */
    fun getStateList(
        ids: Set<String>? = null
    ): List<State> {
        val startEventMap = mutableMapOf<String, MutableMap<Int, State.StartEvent>>()
        for ((key, startEvent) in this.startEventMap) {
            val (configId, mmsi) = key
            if (ids == null || configId in ids) {
                startEventMap.getOrPut(configId) { mutableMapOf() }[mmsi] = startEvent
            }
        }
        return startEventMap.map { (configId, events) ->
            val outside = outsideMap[configId] ?: emptySet()
            State(configId, events, outside)
        }
    }

    fun getClearedAreaList(stateWithStartEvent: List<State>): List<State> {
        // Fill the map with areas that are now empty so these are persisted to the DB as well
        return areasRequiringPersistence.mapNotNull { areaId ->
            if (stateWithStartEvent.any { it._id == areaId }) {
                null
            } else {
                val outside = outsideMap[areaId] ?: emptySet()
                State(areaId, emptyMap(), outside)
            }
        }
    }

    /**
     * Gets the start event (if any), for a specific area [config] and [mmsi]
     */
    fun getStartEventId(
        config: Config,
        mmsi: Int
    ): String? = startEventMap[toKey(config, mmsi)]?.startEventId

    /**
     * Get [Config], [State.StartEvent] and outside status for all areas that [diff] is
     * currently inside or (just) outside.
     */
    fun getConfigsAndStatusForShip(
        diff: AisDiffMessage
    ): List<ConfigShipStatus> {
        val configIds = mmsiConfigMap[diff.mmsi] ?: return emptyList()
        val configs = configService.getConfigs(configIds)
        return configs.map { config ->
            val startEvent = startEventMap[toKey(config._id, diff.mmsi)]
            val outside = outsideMap[config._id]?.contains(diff.mmsi) ?: false
            ConfigShipStatus(config, startEvent, outside)
        }
    }

    data class ConfigShipStatus(
        val config: Config,
        val startEvent: State.StartEvent?,
        val outside: Boolean,
    )

    /**
     * Lightweight ship position data for tracking latest location of all ships
     */
    data class ShipPosition(
        val location: Location,
        val messageTime: Instant
    )

    /**
     * Save a start event for a specific area [config], a ship's [diff] and [startEventId]
     */
    fun saveStartEvent(
        config: Config,
        diff: AisDiffMessage,
        startEventId: String
    ) {
        val startEvent = State.StartEvent(
            diff = diff,
            startEventId = startEventId,
            // don't set the last state event time on purpose, to ensure the first state event
            // is sent upon receiving the next diff message
            lastStateEventSentTime = null
        )
        startEventMap[toKey(config, diff.mmsi)] = startEvent
        mmsiConfigMap.compute(diff.mmsi) { _, configIds ->
            val set = configIds ?: mutableSetOf()
            set.add(config._id)
            set
        }

        // The ship might have left and re-entered the area, ensure we clean up outside state.
        outsideMap.compute(config._id) { _, mmsis ->
            val set = mmsis ?: mutableSetOf()
            set.remove(diff.mmsi)
            set.ifEmpty { null }
        }
    }

    /**
     * Save [lastStateEventSentTime] for a specific area [config] and [diff].
     */
    fun saveLastStateEventSentTime(
        config: Config,
        diff: AisDiffMessage,
        lastStateEventSentTime: Instant
    ) {
        startEventMap.compute(toKey(config, diff.mmsi)) { _, current ->
            current?.copy(lastStateEventSentTime = lastStateEventSentTime)
        }
    }

    /**
     * Delete a start event for a specific area [config] and a ship's [diff]
     */
    fun deleteStartEvent(
        config: Config,
        diff: AisDiffMessage
    ) {
        startEventMap.remove(toKey(config, diff.mmsi))
        outsideMap.compute(config._id) { _, mmsis ->
            val set = mmsis ?: mutableSetOf()
            set.add(diff.mmsi)
            set
        }
        areasRequiringPersistence.add(config._id)
    }

    /**
     * Delete marker for ship's [diff] being outside of area [config].
     */
    fun deleteOutsideEvent(
        config: Config,
        diff: AisDiffMessage
    ) {
        mmsiConfigMap.compute(diff.mmsi) { _, configIds ->
            val set = configIds ?: mutableSetOf()
            set.remove(config._id)
            set.ifEmpty { null }
        }
        outsideMap.compute(config._id) { _, mmsis ->
            val set = mmsis ?: mutableSetOf()
            set.remove(diff.mmsi)
            set.ifEmpty { null }
        }
        areasRequiringPersistence.add(config._id)
    }

    /**
     * Update the [diff] for all areas that the ship is currently in
     */
    fun update(
        diff: AisDiffMessage
    ) {
        // Update area-specific tracking (existing functionality)
        val configIds = mmsiConfigMap[diff.mmsi] ?: return
        configIds.forEach { configId ->
            startEventMap.computeIfPresent(toKey(configId, diff.mmsi)) { _, event ->
                // Only update if latest state
                if (event.diff.messageTime < diff.messageTime) {
                    event.copy(diff = diff)
                } else {
                    event
                }
            }
        }
    }

    /**
     * Update the latest position for a ship (lightweight tracking for ALL ships)
     */
    fun updateShipPosition(diff: AisDiffMessage) {
        val latestLocation = diff.location.latest()
        val newPosition = ShipPosition(
            location = latestLocation,
            messageTime = diff.messageTime
        )

        shipPositions.compute(diff.mmsi) { _, existing ->
            if (existing == null || diff.messageTime > existing.messageTime) {
                positionsRequiringPersistence.add(diff.mmsi)
                newPosition
            } else {
                existing
            }
        }
    }

    /**
     * Get the latest known position for a ship by MMSI
     */
    fun getLatestShipPosition(mmsi: Int): ShipPosition? {
        return shipPositions[mmsi]
    }

    /**
     * Delete all state related to areas with the given [ids] from the database, as well as from memory
     */
    fun delete(ids: Set<String>) {
        stateDataSource.delete(ids)
        removeLocally(ids)
    }

    /**
     * Loads all state related to areas with the given [ids] into memory
     */
    fun loadLocally(
        ids: Set<String>
    ) {
        val configState = stateDataSource.list(ids)
        for (config in configState) {
            for ((mmsi, event) in config.events) {
                startEventMap[toKey(config._id, mmsi)] = event
                mmsiConfigMap.getOrPut(mmsi, ::mutableSetOf).add(config._id)
            }
            if (config.outside.isNotEmpty()) {
                outsideMap[config._id] = config.outside.toMutableSet()
                config.outside.forEach { mmsi ->
                    mmsiConfigMap.getOrPut(mmsi, ::mutableSetOf).add(config._id)
                }
            }
        }
    }

    /**
     * Removes all state related to areas with the given [ids] from memory
     */
    fun removeLocally(
        ids: Set<String>
    ) {
        val deletes = mutableListOf<Pair<String, Int>>()
        for ((key, _) in startEventMap) {
            val (configId, mmsi) = key
            if (configId in ids) {
                deletes.add(key)
                mmsiConfigMap.compute(mmsi) { _, configIds ->
                    val set = configIds ?: mutableSetOf()
                    set.remove(configId)
                    set.ifEmpty { null }
                }
                outsideMap.remove(configId)
            }
        }
        deletes.forEach { startEventMap.remove(it) }
    }

    /**
     * Load ship positions from database into memory
     */
    private fun loadShipPositions() {
        val positions = positionDataSource.list()
        positions.forEach { position ->
            shipPositions[position.mmsi] = ShipPosition(
                location = position.location,
                messageTime = position.messageTime
            )
        }
    }

    /**
     * Persist ship positions that have been updated
     */
    private fun persistShipPositions() {
        if (positionsRequiringPersistence.isEmpty()) {
            return
        }

        val positionsToSave = positionsRequiringPersistence.mapNotNull { mmsi ->
            shipPositions[mmsi]?.let { shipPosition ->
                Position(
                    mmsi = mmsi,
                    location = shipPosition.location,
                    messageTime = shipPosition.messageTime
                )
            }
        }

        if (positionsToSave.isNotEmpty()) {
            positionDataSource.save(positionsToSave)
        }

        positionsRequiringPersistence.clear()
    }
}
